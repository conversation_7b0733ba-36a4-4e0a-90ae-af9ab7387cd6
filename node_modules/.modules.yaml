hoistPattern:
  - '*'
hoistedDependencies:
  '@codemirror/lint@6.8.4':
    '@codemirror/lint': private
  '@esbuild/aix-ppc64@0.25.1':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.1':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.1':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.1':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.1':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.1':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.1':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.1':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.1':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.1':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.1':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.1':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.1':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.1':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.1':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.1':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.1':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.1':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.1':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.1':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.1':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.25.1':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.1':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.1':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.1':
    '@esbuild/win32-x64': private
  '@lezer/common@1.2.3':
    '@lezer/common': private
  '@lezer/highlight@1.2.1':
    '@lezer/highlight': private
  '@lezer/javascript@1.4.21':
    '@lezer/javascript': private
  '@lezer/lr@1.4.2':
    '@lezer/lr': private
  '@marijn/find-cluster-break@1.0.2':
    '@marijn/find-cluster-break': private
  '@rollup/rollup-android-arm-eabi@4.35.0':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.35.0':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.35.0':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.35.0':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.35.0':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.35.0':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.35.0':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.35.0':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.35.0':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.35.0':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.35.0':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-powerpc64le-gnu@4.35.0':
    '@rollup/rollup-linux-powerpc64le-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.35.0':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-s390x-gnu@4.35.0':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.35.0':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.35.0':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.35.0':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.35.0':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.35.0':
    '@rollup/rollup-win32-x64-msvc': private
  '@types/estree@1.0.6':
    '@types/estree': private
  crelt@1.0.6:
    crelt: private
  esbuild@0.25.1:
    esbuild: private
  fsevents@2.3.3:
    fsevents: private
  nanoid@3.3.9:
    nanoid: private
  picocolors@1.1.1:
    picocolors: private
  postcss@8.5.3:
    postcss: private
  rollup@4.35.0:
    rollup: private
  source-map-js@1.2.1:
    source-map-js: private
  style-mod@4.1.2:
    style-mod: private
  w3c-keyname@2.2.8:
    w3c-keyname: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.6.2
pendingBuilds: []
prunedAt: Thu, 13 Mar 2025 01:38:09 GMT
publicHoistPattern: []
registries:
  default: https://registry.npmmirror.com/
skipped:
  - '@esbuild/aix-ppc64@0.25.1'
  - '@esbuild/android-arm64@0.25.1'
  - '@esbuild/android-arm@0.25.1'
  - '@esbuild/android-x64@0.25.1'
  - '@esbuild/darwin-x64@0.25.1'
  - '@esbuild/freebsd-arm64@0.25.1'
  - '@esbuild/freebsd-x64@0.25.1'
  - '@esbuild/linux-arm64@0.25.1'
  - '@esbuild/linux-arm@0.25.1'
  - '@esbuild/linux-ia32@0.25.1'
  - '@esbuild/linux-loong64@0.25.1'
  - '@esbuild/linux-mips64el@0.25.1'
  - '@esbuild/linux-ppc64@0.25.1'
  - '@esbuild/linux-riscv64@0.25.1'
  - '@esbuild/linux-s390x@0.25.1'
  - '@esbuild/linux-x64@0.25.1'
  - '@esbuild/netbsd-arm64@0.25.1'
  - '@esbuild/netbsd-x64@0.25.1'
  - '@esbuild/openbsd-arm64@0.25.1'
  - '@esbuild/openbsd-x64@0.25.1'
  - '@esbuild/sunos-x64@0.25.1'
  - '@esbuild/win32-arm64@0.25.1'
  - '@esbuild/win32-ia32@0.25.1'
  - '@esbuild/win32-x64@0.25.1'
  - '@rollup/rollup-android-arm-eabi@4.35.0'
  - '@rollup/rollup-android-arm64@4.35.0'
  - '@rollup/rollup-darwin-x64@4.35.0'
  - '@rollup/rollup-freebsd-arm64@4.35.0'
  - '@rollup/rollup-freebsd-x64@4.35.0'
  - '@rollup/rollup-linux-arm-gnueabihf@4.35.0'
  - '@rollup/rollup-linux-arm-musleabihf@4.35.0'
  - '@rollup/rollup-linux-arm64-gnu@4.35.0'
  - '@rollup/rollup-linux-arm64-musl@4.35.0'
  - '@rollup/rollup-linux-loongarch64-gnu@4.35.0'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.35.0'
  - '@rollup/rollup-linux-riscv64-gnu@4.35.0'
  - '@rollup/rollup-linux-s390x-gnu@4.35.0'
  - '@rollup/rollup-linux-x64-gnu@4.35.0'
  - '@rollup/rollup-linux-x64-musl@4.35.0'
  - '@rollup/rollup-win32-arm64-msvc@4.35.0'
  - '@rollup/rollup-win32-ia32-msvc@4.35.0'
  - '@rollup/rollup-win32-x64-msvc@4.35.0'
storeDir: /Users/<USER>/Library/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
