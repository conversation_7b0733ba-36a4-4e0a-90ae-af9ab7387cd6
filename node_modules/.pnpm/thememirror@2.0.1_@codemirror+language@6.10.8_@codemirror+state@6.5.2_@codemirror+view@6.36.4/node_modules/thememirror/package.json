{"name": "thememirror", "version": "2.0.1", "description": "Beautiful themes for CodeMirror", "license": "MIT", "repository": "vadimdemedes/thememirror", "author": {"name": "vdemedes", "email": "vadi<PERSON><PERSON><EMAIL>", "url": "https://vadimdemedes.com"}, "type": "module", "exports": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"prepare": "npm run build", "build": "tsc", "dev": "tsc --watch", "test": "prettier --check source && xo"}, "files": ["dist"], "keywords": ["codemirror", "theme", "color scheme"], "devDependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/highlight": "^1.0.0", "@sindresorhus/tsconfig": "^3.0.0", "@vdemedes/prettier-config": "^2.0.1", "prettier": "^2.6.2", "typescript": "^4.7.2", "xo": "^0.49.0"}, "peerDependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0"}, "prettier": "@vdemedes/prettier-config", "xo": {"prettier": true, "rules": {"unicorn/no-abusive-eslint-disable": "off"}}}