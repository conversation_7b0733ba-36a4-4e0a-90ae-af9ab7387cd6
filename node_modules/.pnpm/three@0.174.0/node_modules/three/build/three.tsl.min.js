/**
 * @license
 * Copyright 2010-2025 Three.js Authors
 * SPDX-License-Identifier: MIT
 */
import{TSL as e}from"three/webgpu";const t=e.BRDF_GGX,r=e.BRDF_Lambert,a=e.BasicShadowFilter,o=e.Break,i=e.Continue,n=e.DFGApprox,s=e.D_GGX,l=e.Discard,c=e.EPSILON,m=e.F_Schlick,p=e.Fn,d=e.INFINITY,u=e.If,g=e.Loop,h=e.NodeShaderStage,f=e.NodeType,x=e.NodeUpdateType,b=e.NodeAccess,w=e.PCFShadowFilter,v=e.PCFSoftShadowFilter,S=e.PI,T=e.PI2,_=e.Return,V=e.Schlick_to_F0,y=e.ScriptableNodeResources,D=e.ShaderNode,M=e.TBNViewMatrix,F=e.VSMShadowFilter,C=e.V_GGX_SmithCorrelated,I=e.abs,P=e.acesFilmicToneMapping,R=e.acos,N=e.add,B=e.addNodeElement,L=e.agxToneMapping,k=e.all,A=e.alphaT,G=e.and,O=e.anisotropy,W=e.anisotropyB,j=e.anisotropyT,U=e.any,z=e.append,q=e.array,E=e.arrayBuffer,Z=e.asin,X=e.assign,Y=e.atan,H=e.atan2,J=e.atomicAdd,K=e.atomicAnd,Q=e.atomicFunc,$=e.atomicMax,ee=e.atomicMin,te=e.atomicOr,re=e.atomicStore,ae=e.atomicSub,oe=e.atomicXor,ie=e.atomicLoad,ne=e.attenuationColor,se=e.attenuationDistance,le=e.attribute,ce=e.attributeArray,me=e.backgroundBlurriness,pe=e.backgroundIntensity,de=e.backgroundRotation,ue=e.batch,ge=e.billboarding,he=e.bitAnd,fe=e.bitNot,xe=e.bitOr,be=e.bitXor,we=e.bitangentGeometry,ve=e.bitangentLocal,Se=e.bitangentView,Te=e.bitangentWorld,_e=e.bitcast,Ve=e.blendBurn,ye=e.blendColor,De=e.blendDodge,Me=e.blendOverlay,Fe=e.blendScreen,Ce=e.blur,Ie=e.bool,Pe=e.buffer,Re=e.bufferAttribute,Ne=e.bumpMap,Be=e.burn,Le=e.bvec2,ke=e.bvec3,Ae=e.bvec4,Ge=e.bypass,Oe=e.cache,We=e.call,je=e.cameraFar,Ue=e.cameraIndex,ze=e.cameraNear,qe=e.cameraNormalMatrix,Ee=e.cameraPosition,Ze=e.cameraProjectionMatrix,Xe=e.cameraProjectionMatrixInverse,Ye=e.cameraViewMatrix,He=e.cameraWorldMatrix,Je=e.cbrt,Ke=e.cdl,Qe=e.ceil,$e=e.checker,et=e.cineonToneMapping,tt=e.clamp,rt=e.clearcoat,at=e.clearcoatRoughness,ot=e.code,it=e.color,nt=e.colorSpaceToWorking,st=e.colorToDirection,lt=e.compute,ct=e.cond,mt=e.Const,pt=e.context,dt=e.convert,ut=e.convertColorSpace,gt=e.convertToTexture,ht=e.cos,ft=e.cross,xt=e.cubeTexture,bt=e.dFdx,wt=e.dFdy,vt=e.dashSize,St=e.defaultBuildStages,Tt=e.defaultShaderStages,_t=e.defined,Vt=e.degrees,yt=e.deltaTime,Dt=e.densityFog,Mt=e.densityFogFactor,Ft=e.depth,Ct=e.depthPass,It=e.difference,Pt=e.diffuseColor,Rt=e.directPointLight,Nt=e.directionToColor,Bt=e.dispersion,Lt=e.distance,kt=e.div,At=e.dodge,Gt=e.dot,Ot=e.drawIndex,Wt=e.dynamicBufferAttribute,jt=e.element,Ut=e.emissive,zt=e.equal,qt=e.equals,Et=e.equirectUV,Zt=e.exp,Xt=e.exp2,Yt=e.expression,Ht=e.faceDirection,Jt=e.faceForward,Kt=e.faceforward,Qt=e.float,$t=e.floor,er=e.fog,tr=e.fract,rr=e.frameGroup,ar=e.frameId,or=e.frontFacing,ir=e.fwidth,nr=e.gain,sr=e.gapSize,lr=e.getConstNodeType,cr=e.getCurrentStack,mr=e.getDirection,pr=e.getDistanceAttenuation,dr=e.getGeometryRoughness,ur=e.getNormalFromDepth,gr=e.getParallaxCorrectNormal,hr=e.getRoughness,fr=e.getScreenPosition,xr=e.getShIrradianceAt,br=e.getTextureIndex,wr=e.getViewPosition,vr=e.glsl,Sr=e.glslFn,Tr=e.grayscale,_r=e.greaterThan,Vr=e.greaterThanEqual,yr=e.hash,Dr=e.highpModelNormalViewMatrix,Mr=e.highpModelViewMatrix,Fr=e.hue,Cr=e.instance,Ir=e.instanceIndex,Pr=e.instancedArray,Rr=e.instancedBufferAttribute,Nr=e.instancedDynamicBufferAttribute,Br=e.instancedMesh,Lr=e.int,kr=e.inverseSqrt,Ar=e.inversesqrt,Gr=e.invocationLocalIndex,Or=e.invocationSubgroupIndex,Wr=e.ior,jr=e.iridescence,Ur=e.iridescenceIOR,zr=e.iridescenceThickness,qr=e.ivec2,Er=e.ivec3,Zr=e.ivec4,Xr=e.js,Yr=e.label,Hr=e.length,Jr=e.lengthSq,Kr=e.lessThan,Qr=e.lessThanEqual,$r=e.lightPosition,ea=e.lightTargetDirection,ta=e.lightTargetPosition,ra=e.lightViewPosition,aa=e.lightingContext,oa=e.lights,ia=e.linearDepth,na=e.linearToneMapping,sa=e.localId,la=e.globalId,ca=e.log,ma=e.log2,pa=e.logarithmicDepthToViewZ,da=e.loop,ua=e.luminance,ga=e.mediumpModelViewMatrix,ha=e.mat2,fa=e.mat3,xa=e.mat4,ba=e.matcapUV,wa=e.materialAO,va=e.materialAlphaTest,Sa=e.materialAnisotropy,Ta=e.materialAnisotropyVector,_a=e.materialAttenuationColor,Va=e.materialAttenuationDistance,ya=e.materialClearcoat,Da=e.materialClearcoatNormal,Ma=e.materialClearcoatRoughness,Fa=e.materialColor,Ca=e.materialDispersion,Ia=e.materialEmissive,Pa=e.materialIOR,Ra=e.materialIridescence,Na=e.materialIridescenceIOR,Ba=e.materialIridescenceThickness,La=e.materialLightMap,ka=e.materialLineDashOffset,Aa=e.materialLineDashSize,Ga=e.materialLineGapSize,Oa=e.materialLineScale,Wa=e.materialLineWidth,ja=e.materialMetalness,Ua=e.materialNormal,za=e.materialOpacity,qa=e.materialPointSize,Ea=e.materialReference,Za=e.materialReflectivity,Xa=e.materialRefractionRatio,Ya=e.materialRotation,Ha=e.materialRoughness,Ja=e.materialSheen,Ka=e.materialSheenRoughness,Qa=e.materialShininess,$a=e.materialSpecular,eo=e.materialSpecularColor,to=e.materialSpecularIntensity,ro=e.materialSpecularStrength,ao=e.materialThickness,oo=e.materialTransmission,io=e.max,no=e.maxMipLevel,so=e.metalness,lo=e.min,co=e.mix,mo=e.mixElement,po=e.mod,uo=e.modInt,go=e.modelDirection,ho=e.modelNormalMatrix,fo=e.modelPosition,xo=e.modelRadius,bo=e.modelScale,wo=e.modelViewMatrix,vo=e.modelViewPosition,So=e.modelViewProjection,To=e.modelWorldMatrix,_o=e.modelWorldMatrixInverse,Vo=e.morphReference,yo=e.mrt,Do=e.mul,Mo=e.mx_aastep,Fo=e.mx_cell_noise_float,Co=e.mx_contrast,Io=e.mx_fractal_noise_float,Po=e.mx_fractal_noise_vec2,Ro=e.mx_fractal_noise_vec3,No=e.mx_fractal_noise_vec4,Bo=e.mx_hsvtorgb,Lo=e.mx_noise_float,ko=e.mx_noise_vec3,Ao=e.mx_noise_vec4,Go=e.mx_ramplr,Oo=e.mx_ramptb,Wo=e.mx_rgbtohsv,jo=e.mx_safepower,Uo=e.mx_splitlr,zo=e.mx_splittb,qo=e.mx_srgb_texture_to_lin_rec709,Eo=e.mx_transform_uv,Zo=e.mx_worley_noise_float,Xo=e.mx_worley_noise_vec2,Yo=e.mx_worley_noise_vec3,Ho=e.negate,Jo=e.neutralToneMapping,Ko=e.nodeArray,Qo=e.nodeImmutable,$o=e.nodeObject,ei=e.nodeObjects,ti=e.nodeProxy,ri=e.normalFlat,ai=e.normalGeometry,oi=e.normalLocal,ii=e.normalMap,ni=e.normalView,si=e.normalWorld,li=e.normalize,ci=e.not,mi=e.notEqual,pi=e.numWorkgroups,di=e.objectDirection,ui=e.objectGroup,gi=e.objectPosition,hi=e.objectRadius,fi=e.objectScale,xi=e.objectViewPosition,bi=e.objectWorldMatrix,wi=e.oneMinus,vi=e.or,Si=e.orthographicDepthToViewZ,Ti=e.oscSawtooth,_i=e.oscSine,Vi=e.oscSquare,yi=e.oscTriangle,Di=e.output,Mi=e.outputStruct,Fi=e.overlay,Ci=e.overloadingFn,Ii=e.parabola,Pi=e.parallaxDirection,Ri=e.parallaxUV,Ni=e.parameter,Bi=e.pass,Li=e.passTexture,ki=e.pcurve,Ai=e.perspectiveDepthToViewZ,Gi=e.pmremTexture,Oi=e.pointUV,Wi=e.pointWidth,ji=e.positionGeometry,Ui=e.positionLocal,zi=e.positionPrevious,qi=e.positionView,Ei=e.positionViewDirection,Zi=e.positionWorld,Xi=e.positionWorldDirection,Yi=e.posterize,Hi=e.pow,Ji=e.pow2,Ki=e.pow3,Qi=e.pow4,$i=e.property,en=e.radians,tn=e.rand,rn=e.range,an=e.rangeFog,on=e.rangeFogFactor,nn=e.reciprocal,sn=e.lightProjectionUV,ln=e.reference,cn=e.referenceBuffer,mn=e.reflect,pn=e.reflectVector,dn=e.reflectView,un=e.reflector,gn=e.refract,hn=e.refractVector,fn=e.refractView,xn=e.reinhardToneMapping,bn=e.remainder,wn=e.remap,vn=e.remapClamp,Sn=e.renderGroup,Tn=e.renderOutput,_n=e.rendererReference,Vn=e.rotate,yn=e.rotateUV,Dn=e.roughness,Mn=e.round,Fn=e.rtt,Cn=e.sRGBTransferEOTF,In=e.sRGBTransferOETF,Pn=e.sampler,Rn=e.saturate,Nn=e.saturation,Bn=e.screen,Ln=e.screenCoordinate,kn=e.screenSize,An=e.screenUV,Gn=e.scriptable,On=e.scriptableValue,Wn=e.select,jn=e.setCurrentStack,Un=e.shaderStages,zn=e.shadow,qn=e.pointShadow,En=e.shadowPositionWorld,Zn=e.sharedUniformGroup,Xn=e.shapeCircle,Yn=e.sheen,Hn=e.sheenRoughness,Jn=e.shiftLeft,Kn=e.shiftRight,Qn=e.shininess,$n=e.sign,es=e.sin,ts=e.sinc,rs=e.skinning,as=e.skinningReference,os=e.smoothstep,is=e.smoothstepElement,ns=e.specularColor,ss=e.specularF90,ls=e.spherizeUV,cs=e.split,ms=e.spritesheetUV,ps=e.sqrt,ds=e.stack,us=e.step,gs=e.storage,hs=e.storageBarrier,fs=e.storageObject,xs=e.storageTexture,bs=e.string,ws=e.struct,vs=e.sub,Ss=e.subgroupIndex,Ts=e.subgroupSize,_s=e.tan,Vs=e.tangentGeometry,ys=e.tangentLocal,Ds=e.tangentView,Ms=e.tangentWorld,Fs=e.temp,Cs=e.texture,Is=e.texture3D,Ps=e.textureBarrier,Rs=e.textureBicubic,Ns=e.textureCubeUV,Bs=e.textureLoad,Ls=e.textureSize,ks=e.textureStore,As=e.thickness,Gs=e.threshold,Os=e.time,Ws=e.timerDelta,js=e.timerGlobal,Us=e.timerLocal,zs=e.toOutputColorSpace,qs=e.toWorkingColorSpace,Es=e.toneMapping,Zs=e.toneMappingExposure,Xs=e.toonOutlinePass,Ys=e.transformDirection,Hs=e.transformNormal,Js=e.transformNormalToView,Ks=e.transformedBentNormalView,Qs=e.transformedBitangentView,$s=e.transformedBitangentWorld,el=e.transformedClearcoatNormalView,tl=e.transformedNormalView,rl=e.transformedNormalWorld,al=e.transformedTangentView,ol=e.transformedTangentWorld,il=e.transmission,nl=e.transpose,sl=e.tri,ll=e.tri3,cl=e.triNoise3D,ml=e.triplanarTexture,pl=e.triplanarTextures,dl=e.trunc,ul=e.tslFn,gl=e.uint,hl=e.uniform,fl=e.uniformArray,xl=e.uniformGroup,bl=e.uniforms,wl=e.userData,vl=e.uv,Sl=e.uvec2,Tl=e.uvec3,_l=e.uvec4,Vl=e.Var,yl=e.varying,Dl=e.varyingProperty,Ml=e.vec2,Fl=e.vec3,Cl=e.vec4,Il=e.vectorComponents,Pl=e.velocity,Rl=e.vertexColor,Nl=e.vertexIndex,Bl=e.vibrance,Ll=e.viewZToLogarithmicDepth,kl=e.viewZToOrthographicDepth,Al=e.viewZToPerspectiveDepth,Gl=e.viewport,Ol=e.viewportBottomLeft,Wl=e.viewportCoordinate,jl=e.viewportDepthTexture,Ul=e.viewportLinearDepth,zl=e.viewportMipTexture,ql=e.viewportResolution,El=e.viewportSafeUV,Zl=e.viewportSharedTexture,Xl=e.viewportSize,Yl=e.viewportTexture,Hl=e.viewportTopLeft,Jl=e.viewportUV,Kl=e.wgsl,Ql=e.wgslFn,$l=e.workgroupArray,ec=e.workgroupBarrier,tc=e.workgroupId,rc=e.workingToColorSpace,ac=e.xor;export{t as BRDF_GGX,r as BRDF_Lambert,a as BasicShadowFilter,o as Break,mt as Const,i as Continue,n as DFGApprox,s as D_GGX,l as Discard,c as EPSILON,m as F_Schlick,p as Fn,d as INFINITY,u as If,g as Loop,b as NodeAccess,h as NodeShaderStage,f as NodeType,x as NodeUpdateType,w as PCFShadowFilter,v as PCFSoftShadowFilter,S as PI,T as PI2,_ as Return,V as Schlick_to_F0,y as ScriptableNodeResources,D as ShaderNode,M as TBNViewMatrix,F as VSMShadowFilter,C as V_GGX_SmithCorrelated,Vl as Var,I as abs,P as acesFilmicToneMapping,R as acos,N as add,B as addNodeElement,L as agxToneMapping,k as all,A as alphaT,G as and,O as anisotropy,W as anisotropyB,j as anisotropyT,U as any,z as append,q as array,E as arrayBuffer,Z as asin,X as assign,Y as atan,H as atan2,J as atomicAdd,K as atomicAnd,Q as atomicFunc,ie as atomicLoad,$ as atomicMax,ee as atomicMin,te as atomicOr,re as atomicStore,ae as atomicSub,oe as atomicXor,ne as attenuationColor,se as attenuationDistance,le as attribute,ce as attributeArray,me as backgroundBlurriness,pe as backgroundIntensity,de as backgroundRotation,ue as batch,ge as billboarding,he as bitAnd,fe as bitNot,xe as bitOr,be as bitXor,we as bitangentGeometry,ve as bitangentLocal,Se as bitangentView,Te as bitangentWorld,_e as bitcast,Ve as blendBurn,ye as blendColor,De as blendDodge,Me as blendOverlay,Fe as blendScreen,Ce as blur,Ie as bool,Pe as buffer,Re as bufferAttribute,Ne as bumpMap,Be as burn,Le as bvec2,ke as bvec3,Ae as bvec4,Ge as bypass,Oe as cache,We as call,je as cameraFar,Ue as cameraIndex,ze as cameraNear,qe as cameraNormalMatrix,Ee as cameraPosition,Ze as cameraProjectionMatrix,Xe as cameraProjectionMatrixInverse,Ye as cameraViewMatrix,He as cameraWorldMatrix,Je as cbrt,Ke as cdl,Qe as ceil,$e as checker,et as cineonToneMapping,tt as clamp,rt as clearcoat,at as clearcoatRoughness,ot as code,it as color,nt as colorSpaceToWorking,st as colorToDirection,lt as compute,ct as cond,pt as context,dt as convert,ut as convertColorSpace,gt as convertToTexture,ht as cos,ft as cross,xt as cubeTexture,bt as dFdx,wt as dFdy,vt as dashSize,St as defaultBuildStages,Tt as defaultShaderStages,_t as defined,Vt as degrees,yt as deltaTime,Dt as densityFog,Mt as densityFogFactor,Ft as depth,Ct as depthPass,It as difference,Pt as diffuseColor,Rt as directPointLight,Nt as directionToColor,Bt as dispersion,Lt as distance,kt as div,At as dodge,Gt as dot,Ot as drawIndex,Wt as dynamicBufferAttribute,jt as element,Ut as emissive,zt as equal,qt as equals,Et as equirectUV,Zt as exp,Xt as exp2,Yt as expression,Ht as faceDirection,Jt as faceForward,Kt as faceforward,Qt as float,$t as floor,er as fog,tr as fract,rr as frameGroup,ar as frameId,or as frontFacing,ir as fwidth,nr as gain,sr as gapSize,lr as getConstNodeType,cr as getCurrentStack,mr as getDirection,pr as getDistanceAttenuation,dr as getGeometryRoughness,ur as getNormalFromDepth,gr as getParallaxCorrectNormal,hr as getRoughness,fr as getScreenPosition,xr as getShIrradianceAt,br as getTextureIndex,wr as getViewPosition,la as globalId,vr as glsl,Sr as glslFn,Tr as grayscale,_r as greaterThan,Vr as greaterThanEqual,yr as hash,Dr as highpModelNormalViewMatrix,Mr as highpModelViewMatrix,Fr as hue,Cr as instance,Ir as instanceIndex,Pr as instancedArray,Rr as instancedBufferAttribute,Nr as instancedDynamicBufferAttribute,Br as instancedMesh,Lr as int,kr as inverseSqrt,Ar as inversesqrt,Gr as invocationLocalIndex,Or as invocationSubgroupIndex,Wr as ior,jr as iridescence,Ur as iridescenceIOR,zr as iridescenceThickness,qr as ivec2,Er as ivec3,Zr as ivec4,Xr as js,Yr as label,Hr as length,Jr as lengthSq,Kr as lessThan,Qr as lessThanEqual,$r as lightPosition,sn as lightProjectionUV,ea as lightTargetDirection,ta as lightTargetPosition,ra as lightViewPosition,aa as lightingContext,oa as lights,ia as linearDepth,na as linearToneMapping,sa as localId,ca as log,ma as log2,pa as logarithmicDepthToViewZ,da as loop,ua as luminance,ha as mat2,fa as mat3,xa as mat4,ba as matcapUV,wa as materialAO,va as materialAlphaTest,Sa as materialAnisotropy,Ta as materialAnisotropyVector,_a as materialAttenuationColor,Va as materialAttenuationDistance,ya as materialClearcoat,Da as materialClearcoatNormal,Ma as materialClearcoatRoughness,Fa as materialColor,Ca as materialDispersion,Ia as materialEmissive,Pa as materialIOR,Ra as materialIridescence,Na as materialIridescenceIOR,Ba as materialIridescenceThickness,La as materialLightMap,ka as materialLineDashOffset,Aa as materialLineDashSize,Ga as materialLineGapSize,Oa as materialLineScale,Wa as materialLineWidth,ja as materialMetalness,Ua as materialNormal,za as materialOpacity,qa as materialPointSize,Ea as materialReference,Za as materialReflectivity,Xa as materialRefractionRatio,Ya as materialRotation,Ha as materialRoughness,Ja as materialSheen,Ka as materialSheenRoughness,Qa as materialShininess,$a as materialSpecular,eo as materialSpecularColor,to as materialSpecularIntensity,ro as materialSpecularStrength,ao as materialThickness,oo as materialTransmission,io as max,no as maxMipLevel,ga as mediumpModelViewMatrix,so as metalness,lo as min,co as mix,mo as mixElement,po as mod,uo as modInt,go as modelDirection,ho as modelNormalMatrix,fo as modelPosition,xo as modelRadius,bo as modelScale,wo as modelViewMatrix,vo as modelViewPosition,So as modelViewProjection,To as modelWorldMatrix,_o as modelWorldMatrixInverse,Vo as morphReference,yo as mrt,Do as mul,Mo as mx_aastep,Fo as mx_cell_noise_float,Co as mx_contrast,Io as mx_fractal_noise_float,Po as mx_fractal_noise_vec2,Ro as mx_fractal_noise_vec3,No as mx_fractal_noise_vec4,Bo as mx_hsvtorgb,Lo as mx_noise_float,ko as mx_noise_vec3,Ao as mx_noise_vec4,Go as mx_ramplr,Oo as mx_ramptb,Wo as mx_rgbtohsv,jo as mx_safepower,Uo as mx_splitlr,zo as mx_splittb,qo as mx_srgb_texture_to_lin_rec709,Eo as mx_transform_uv,Zo as mx_worley_noise_float,Xo as mx_worley_noise_vec2,Yo as mx_worley_noise_vec3,Ho as negate,Jo as neutralToneMapping,Ko as nodeArray,Qo as nodeImmutable,$o as nodeObject,ei as nodeObjects,ti as nodeProxy,ri as normalFlat,ai as normalGeometry,oi as normalLocal,ii as normalMap,ni as normalView,si as normalWorld,li as normalize,ci as not,mi as notEqual,pi as numWorkgroups,di as objectDirection,ui as objectGroup,gi as objectPosition,hi as objectRadius,fi as objectScale,xi as objectViewPosition,bi as objectWorldMatrix,wi as oneMinus,vi as or,Si as orthographicDepthToViewZ,Ti as oscSawtooth,_i as oscSine,Vi as oscSquare,yi as oscTriangle,Di as output,Mi as outputStruct,Fi as overlay,Ci as overloadingFn,Ii as parabola,Pi as parallaxDirection,Ri as parallaxUV,Ni as parameter,Bi as pass,Li as passTexture,ki as pcurve,Ai as perspectiveDepthToViewZ,Gi as pmremTexture,qn as pointShadow,Oi as pointUV,Wi as pointWidth,ji as positionGeometry,Ui as positionLocal,zi as positionPrevious,qi as positionView,Ei as positionViewDirection,Zi as positionWorld,Xi as positionWorldDirection,Yi as posterize,Hi as pow,Ji as pow2,Ki as pow3,Qi as pow4,$i as property,en as radians,tn as rand,rn as range,an as rangeFog,on as rangeFogFactor,nn as reciprocal,ln as reference,cn as referenceBuffer,mn as reflect,pn as reflectVector,dn as reflectView,un as reflector,gn as refract,hn as refractVector,fn as refractView,xn as reinhardToneMapping,bn as remainder,wn as remap,vn as remapClamp,Sn as renderGroup,Tn as renderOutput,_n as rendererReference,Vn as rotate,yn as rotateUV,Dn as roughness,Mn as round,Fn as rtt,Cn as sRGBTransferEOTF,In as sRGBTransferOETF,Pn as sampler,Rn as saturate,Nn as saturation,Bn as screen,Ln as screenCoordinate,kn as screenSize,An as screenUV,Gn as scriptable,On as scriptableValue,Wn as select,jn as setCurrentStack,Un as shaderStages,zn as shadow,En as shadowPositionWorld,Xn as shapeCircle,Zn as sharedUniformGroup,Yn as sheen,Hn as sheenRoughness,Jn as shiftLeft,Kn as shiftRight,Qn as shininess,$n as sign,es as sin,ts as sinc,rs as skinning,as as skinningReference,os as smoothstep,is as smoothstepElement,ns as specularColor,ss as specularF90,ls as spherizeUV,cs as split,ms as spritesheetUV,ps as sqrt,ds as stack,us as step,gs as storage,hs as storageBarrier,fs as storageObject,xs as storageTexture,bs as string,ws as struct,vs as sub,Ss as subgroupIndex,Ts as subgroupSize,_s as tan,Vs as tangentGeometry,ys as tangentLocal,Ds as tangentView,Ms as tangentWorld,Fs as temp,Cs as texture,Is as texture3D,Ps as textureBarrier,Rs as textureBicubic,Ns as textureCubeUV,Bs as textureLoad,Ls as textureSize,ks as textureStore,As as thickness,Gs as threshold,Os as time,Ws as timerDelta,js as timerGlobal,Us as timerLocal,zs as toOutputColorSpace,qs as toWorkingColorSpace,Es as toneMapping,Zs as toneMappingExposure,Xs as toonOutlinePass,Ys as transformDirection,Hs as transformNormal,Js as transformNormalToView,Ks as transformedBentNormalView,Qs as transformedBitangentView,$s as transformedBitangentWorld,el as transformedClearcoatNormalView,tl as transformedNormalView,rl as transformedNormalWorld,al as transformedTangentView,ol as transformedTangentWorld,il as transmission,nl as transpose,sl as tri,ll as tri3,cl as triNoise3D,ml as triplanarTexture,pl as triplanarTextures,dl as trunc,ul as tslFn,gl as uint,hl as uniform,fl as uniformArray,xl as uniformGroup,bl as uniforms,wl as userData,vl as uv,Sl as uvec2,Tl as uvec3,_l as uvec4,yl as varying,Dl as varyingProperty,Ml as vec2,Fl as vec3,Cl as vec4,Il as vectorComponents,Pl as velocity,Rl as vertexColor,Nl as vertexIndex,Bl as vibrance,Ll as viewZToLogarithmicDepth,kl as viewZToOrthographicDepth,Al as viewZToPerspectiveDepth,Gl as viewport,Ol as viewportBottomLeft,Wl as viewportCoordinate,jl as viewportDepthTexture,Ul as viewportLinearDepth,zl as viewportMipTexture,ql as viewportResolution,El as viewportSafeUV,Zl as viewportSharedTexture,Xl as viewportSize,Yl as viewportTexture,Hl as viewportTopLeft,Jl as viewportUV,Kl as wgsl,Ql as wgslFn,$l as workgroupArray,ec as workgroupBarrier,tc as workgroupId,rc as workingToColorSpace,ac as xor};
