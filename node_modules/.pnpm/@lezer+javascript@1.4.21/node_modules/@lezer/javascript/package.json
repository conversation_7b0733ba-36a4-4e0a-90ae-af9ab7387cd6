{"name": "@lezer/javascript", "version": "1.4.21", "description": "lezer-based JavaScript grammar", "main": "dist/index.cjs", "type": "module", "exports": {"import": "./dist/index.js", "require": "./dist/index.cjs"}, "module": "dist/index.js", "types": "dist/index.d.ts", "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "devDependencies": {"@lezer/generator": "^1.0.0", "mocha": "^10.2.0", "rollup": "^2.52.2", "@rollup/plugin-node-resolve": "^9.0.0"}, "dependencies": {"@lezer/lr": "^1.3.0", "@lezer/common": "^1.2.0", "@lezer/highlight": "^1.1.3"}, "repository": {"type": "git", "url": "https://github.com/lezer-parser/javascript.git"}, "scripts": {"build": "lezer-generator src/javascript.grammar -o src/parser && rollup -c", "build-debug": "lezer-generator src/javascript.grammar --names -o src/parser && rollup -c", "prepare": "npm run build", "test": "mocha test/test-*.js"}}