// This file was generated by lezer-generator. You probably shouldn't edit it.
export const
  noSemi = 314,
  noSemiType = 315,
  incdec = 1,
  incdecPrefix = 2,
  questionDot = 3,
  JSXStartTag = 4,
  insertSemi = 316,
  spaces = 318,
  newline = 319,
  LineComment = 5,
  BlockComment = 6,
  Script = 7,
  Hashbang = 8,
  ExportDeclaration = 9,
  _export = 10,
  Star = 11,
  as = 12,
  VariableName = 13,
  String = 14,
  Escape = 15,
  from = 16,
  _default = 18,
  FunctionDeclaration = 19,
  async = 62,
  _function = 21,
  VariableDefinition = 22,
  TypeParamList = 25,
  _const = 26,
  TypeDefinition = 27,
  _extends = 28,
  _this = 30,
  Number = 33,
  BooleanLiteral = 34,
  TemplateType = 35,
  InterpolationEnd = 36,
  templateType = 37,
  InterpolationStart = 38,
  _null = 40,
  _void = 42,
  _typeof = 44,
  PropertyName = 47,
  TemplateString = 49,
  templateEscape = 50,
  templateExpr = 51,
  _super = 52,
  RegExp = 53,
  ArrayExpression = 55,
  Property = 61,
  get = 63,
  set = 64,
  PropertyDefinition = 65,
  Block = 66,
  _new = 199,
  ArgList = 73,
  UnaryExpression = 74,
  _delete = 75,
  YieldExpression = 78,
  _yield = 79,
  AwaitExpression = 80,
  _await = 81,
  ParenthesizedExpression = 82,
  ClassExpression = 83,
  _class = 84,
  ClassBody = 85,
  MethodDeclaration = 86,
  Decorator = 87,
  PrivatePropertyName = 90,
  TypeArgList = 92,
  LessThan = 93,
  declare = 224,
  Privacy = 115,
  _static = 97,
  abstract = 207,
  override = 99,
  PrivatePropertyDefinition = 100,
  PropertyDeclaration = 101,
  readonly = 116,
  accessor = 103,
  Optional = 104,
  TypeAnnotation = 105,
  StaticBlock = 107,
  FunctionExpression = 108,
  ArrowFunction = 109,
  ParamList = 111,
  ArrayPattern = 112,
  ObjectPattern = 113,
  PatternProperty = 114,
  MemberExpression = 118,
  BinaryExpression = 119,
  divide = 121,
  _instanceof = 126,
  satisfies = 127,
  _in = 128,
  questionOp = 136,
  AssignmentExpression = 138,
  _import = 145,
  JSXElement = 147,
  JSXSelfCloseEndTag = 148,
  JSXSelfClosingTag = 149,
  JSXIdentifier = 150,
  JSXLowerIdentifier = 152,
  JSXNamespacedName = 153,
  JSXMemberExpression = 154,
  JSXAttributeValue = 157,
  JSXEndTag = 159,
  JSXOpenTag = 160,
  JSXFragmentTag = 161,
  JSXText = 162,
  JSXEscape = 163,
  JSXStartCloseTag = 164,
  JSXCloseTag = 165,
  tsAngleOpen = 167,
  SequenceExpression = 170,
  keyof = 173,
  unique = 175,
  infer = 178,
  TypeName = 179,
  ParamTypeList = 182,
  IndexedType = 184,
  Label = 186,
  ObjectType = 189,
  MethodType = 190,
  PropertyType = 191,
  IndexSignature = 192,
  TypePredicate = 195,
  asserts = 196,
  is = 197,
  unionOp = 201,
  intersectionOp = 203,
  ClassDeclaration = 206,
  _implements = 208,
  type = 209,
  VariableDeclaration = 210,
  _let = 211,
  _var = 212,
  using = 213,
  TypeAliasDeclaration = 214,
  InterfaceDeclaration = 215,
  _interface = 216,
  EnumDeclaration = 217,
  _enum = 218,
  NamespaceDeclaration = 220,
  namespace = 221,
  module = 222,
  AmbientDeclaration = 223,
  global = 226,
  ExportGroup = 230,
  ImportDeclaration = 233,
  ImportGroup = 234,
  _for = 236,
  ForSpec = 237,
  ForInSpec = 238,
  ForOfSpec = 239,
  of = 240,
  _while = 242,
  _with = 244,
  _do = 246,
  _if = 248,
  _else = 249,
  _switch = 251,
  _case = 254,
  _try = 257,
  _catch = 259,
  _finally = 261,
  _return = 263,
  _throw = 265,
  _break = 267,
  _continue = 269,
  _debugger = 271,
  SingleExpression = 274,
  SingleClassItem = 275,
  Dialect_jsx = 0,
  Dialect_ts = 1
