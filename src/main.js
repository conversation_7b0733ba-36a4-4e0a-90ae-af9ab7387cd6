import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';

// 初始化场景、相机和渲染器
const scene = new THREE.Scene();
const camera = new THREE.PerspectiveCamera(
	75,
	window.innerWidth / window.innerHeight,
	0.1,
	1000
);
camera.position.z = 50;

const renderer = new THREE.WebGLRenderer({ antialias: true });
renderer.setSize(window.innerWidth, window.innerHeight);
document.body.appendChild(renderer.domElement);

// 添加光源
const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
scene.add(ambientLight);
const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
directionalLight.position.set(10, 10, 5);
scene.add(directionalLight);

// 创建纹理加载器
const textureLoader = new THREE.TextureLoader();

// 加载贴图
const texture1 = textureLoader.load('/1.png');
const texture2 = textureLoader.load('/2.png');

// 创建GLTF加载器
const gltfLoader = new GLTFLoader();

let i = 0;
// 加载GLB模型
gltfLoader.load(
	'/1.glb',
	function (gltf) {
		const model = gltf.scene;

		// 遍历模型中的所有网格，应用贴图
		model.traverse(function (child) {
			if (child.isMesh) {
				// 根据需要应用不同的贴图
				// // 这里可以根据材质名称或网格名称来决定使用哪个贴图
				// if (child.material) {
				// 	// 创建新材质并应用贴图
				// 	const material = new THREE.MeshStandardMaterial({
				// 		map: texture1, // 主贴图
				// 		// 如果需要，可以添加其他贴图
				// 		// roughnessMap: texture2, // 粗糙度贴图
				// 	});
				// 	child.material = material;
				// }

				if (child.name == '旗袍女01') {
					child.material = new THREE.MeshStandardMaterial({
						map: texture2,
					});
				}
				if (child.name == '旗袍女01_头发') {
					child.material = new THREE.MeshStandardMaterial({
						map: texture1,
					});
				}
				console.log(child.name);
			}
		});

		// 调整模型大小和位置
		const box = new THREE.Box3().setFromObject(model);
		const center = box.getCenter(new THREE.Vector3());
		const size = box.getSize(new THREE.Vector3());

		// 将模型居中
		model.position.sub(center);

		// 根据模型大小调整相机位置
		const maxDim = Math.max(size.x, size.y, size.z);
		camera.position.z = maxDim * 2;

		// 将模型添加到场景
		scene.add(model);

		console.log('GLB模型加载成功');
	},
	function (progress) {
		console.log('加载进度:', (progress.loaded / progress.total) * 100 + '%');
	},
	function (error) {
		console.error('GLB模型加载失败:', error);
	}
);

// 添加轨道控制器
const controls = new OrbitControls(camera, renderer.domElement);
controls.enableDamping = true; // 启用阻尼效果
controls.dampingFactor = 0.05;

// 窗口大小调整处理
window.addEventListener('resize', () => {
	camera.aspect = window.innerWidth / window.innerHeight;
	camera.updateProjectionMatrix();
	renderer.setSize(window.innerWidth, window.innerHeight);
});

// 动画循环
function animate() {
	requestAnimationFrame(animate);
	controls.update(); // 更新控制器
	renderer.render(scene, camera);
}

animate();
